from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from .. import db
from ..models.user import User
from ..models.parking_lot import ParkingLot
from ..models.parking_spot import ParkingSpot
from ..models.reservation import Reservation
from datetime import datetime

api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.route('/login', methods=['POST'])
def login():
    """API endpoint for user login."""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    user = User.query.filter_by(username=username).first()
    
    if user and user.check_password(password):
        # In a real app, you'd generate a JWT token here
        return jsonify({
            'success': True,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'full_name': user.full_name,
                'role': user.role
            },
            'token': 'dummy-token'  # Replace with real JWT token
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Invalid username or password'
        }), 401

@api_bp.route('/register', methods=['POST'])
def register():
    """API endpoint for user registration."""
    data = request.get_json()
    
    # Check if user already exists
    existing_user = User.query.filter_by(username=data.get('email')).first()
    if existing_user:
        return jsonify({
            'success': False,
            'message': 'User already exists'
        }), 400
    
    # Create new user
    new_user = User(
        username=data.get('email'),
        email=data.get('email'),
        full_name=data.get('full_name'),
        role='user'
    )
    new_user.set_password(data.get('password'))
    
    try:
        db.session.add(new_user)
        db.session.commit()
        return jsonify({
            'success': True,
            'message': 'Registration successful'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@api_bp.route('/logout', methods=['POST'])
@login_required
def logout():
    """API endpoint for user logout."""
    return jsonify({'success': True})

# Parking Lot Management Endpoints
@api_bp.route('/parking-lots', methods=['GET'])
@login_required
def get_parking_lots():
    """Get all parking lots."""
    lots = ParkingLot.query.all()
    return jsonify([{
        'id': lot.id,
        'prime_location_name': lot.prime_location_name,
        'price': lot.price,
        'address': lot.address,
        'pin_code': lot.pin_code,
        'number_of_spots': lot.number_of_spots,
        'available_spots': len([spot for spot in lot.spots if spot.status == 'A'])
    } for lot in lots])

@api_bp.route('/parking-lots', methods=['POST'])
@login_required
def create_parking_lot():
    """Create a new parking lot."""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Admin access required'}), 403
    
    data = request.get_json()
    
    try:
        new_lot = ParkingLot(
            prime_location_name=data.get('name'),
            price=float(data.get('price')),
            address=data.get('address'),
            pin_code=data.get('pinCode'),
            number_of_spots=int(data.get('spots'))
        )
        
        db.session.add(new_lot)
        db.session.commit()
        
        # Create parking spots for this lot
        for i in range(new_lot.number_of_spots):
            spot = ParkingSpot(
                parking_lot_id=new_lot.id,
                status='A'  # Available
            )
            db.session.add(spot)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Parking lot created successfully',
            'lot': {
                'id': new_lot.id,
                'prime_location_name': new_lot.prime_location_name,
                'price': new_lot.price,
                'address': new_lot.address,
                'pin_code': new_lot.pin_code,
                'number_of_spots': new_lot.number_of_spots,
                'available_spots': new_lot.number_of_spots
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Failed to create parking lot: {str(e)}'
        }), 500

@api_bp.route('/parking-lots/<int:lot_id>', methods=['PUT'])
@login_required
def update_parking_lot(lot_id):
    """Update a parking lot."""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Admin access required'}), 403
    
    lot = ParkingLot.query.get_or_404(lot_id)
    data = request.get_json()
    
    try:
        lot.prime_location_name = data.get('name', lot.prime_location_name)
        lot.price = float(data.get('price', lot.price))
        lot.address = data.get('address', lot.address)
        lot.pin_code = data.get('pinCode', lot.pin_code)
        
        # Handle spot count changes
        new_spot_count = int(data.get('spots', lot.number_of_spots))
        current_spots = len(lot.spots)
        
        if new_spot_count > current_spots:
            # Add more spots
            for i in range(new_spot_count - current_spots):
                spot = ParkingSpot(
                    parking_lot_id=lot.id,
                    status='A'
                )
                db.session.add(spot)
        elif new_spot_count < current_spots:
            # Remove spots (only if they're available)
            available_spots = [spot for spot in lot.spots if spot.status == 'A']
            spots_to_remove = current_spots - new_spot_count
            
            if len(available_spots) >= spots_to_remove:
                for spot in available_spots[:spots_to_remove]:
                    db.session.delete(spot)
            else:
                return jsonify({
                    'success': False,
                    'message': 'Cannot reduce spots - some spots are occupied'
                }), 400
        
        lot.number_of_spots = new_spot_count
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Parking lot updated successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Failed to update parking lot: {str(e)}'
        }), 500

@api_bp.route('/parking-lots/<int:lot_id>', methods=['DELETE'])
@login_required
def delete_parking_lot(lot_id):
    """Delete a parking lot."""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Admin access required'}), 403
    
    lot = ParkingLot.query.get_or_404(lot_id)
    
    # Check if all spots are available
    occupied_spots = [spot for spot in lot.spots if spot.status == 'O']
    if occupied_spots:
        return jsonify({
            'success': False,
            'message': 'Cannot delete lot - some spots are occupied'
        }), 400
    
    try:
        # Delete all spots first
        for spot in lot.spots:
            db.session.delete(spot)
        
        # Delete the lot
        db.session.delete(lot)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Parking lot deleted successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Failed to delete parking lot: {str(e)}'
        }), 500

@api_bp.route('/parking-lots/<int:lot_id>', methods=['GET'])
@login_required
def get_parking_lot_details(lot_id):
    """Get detailed information about a parking lot."""
    lot = ParkingLot.query.get_or_404(lot_id)
    
    spots = []
    for spot in lot.spots:
        spot_data = {
            'id': spot.id,
            'status': spot.status,
            'status_text': 'Available' if spot.status == 'A' else 'Occupied'
        }
        
        # Add reservation info if occupied
        if spot.status == 'O':
            reservation = Reservation.query.filter_by(
                spot_id=spot.id,
                leaving_timestamp=None
            ).first()
            if reservation:
                spot_data['reservation'] = {
                    'user_name': reservation.user.full_name,
                    'parking_timestamp': reservation.parking_timestamp.isoformat(),
                    'duration': (datetime.utcnow() - reservation.parking_timestamp).total_seconds() / 3600
                }
        
        spots.append(spot_data)
    
    return jsonify({
        'id': lot.id,
        'prime_location_name': lot.prime_location_name,
        'price': lot.price,
        'address': lot.address,
        'pin_code': lot.pin_code,
        'number_of_spots': lot.number_of_spots,
        'available_spots': len([spot for spot in lot.spots if spot.status == 'A']),
        'spots': spots
    })

@api_bp.route('/parking-lots/<int:lot_id>/book', methods=['POST'])
@login_required
def book_spot(lot_id):
    """Book a parking spot in the specified lot."""
    lot = ParkingLot.query.get_or_404(lot_id)
    
    # Check if user already has an active reservation
    active_reservation = Reservation.query.filter_by(
        user_id=current_user.id,
        leaving_timestamp=None
    ).first()
    
    if active_reservation:
        return jsonify({
            'success': False,
            'message': 'You already have an active reservation'
        }), 400
    
    # Find an available spot
    available_spot = ParkingSpot.query.filter_by(
        parking_lot_id=lot_id,
        status='A'
    ).first()
    
    if not available_spot:
        return jsonify({
            'success': False,
            'message': 'No available spots in this lot'
        }), 400
    
    try:
        # Create reservation
        reservation = Reservation(
            spot_id=available_spot.id,
            user_id=current_user.id,
            parking_timestamp=datetime.utcnow()
        )
        
        # Update spot status
        available_spot.status = 'O'
        
        db.session.add(reservation)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Spot booked successfully',
            'reservation': {
                'id': reservation.id,
                'spot_id': available_spot.id,
                'parking_timestamp': reservation.parking_timestamp.isoformat()
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Failed to book spot: {str(e)}'
        }), 500

@api_bp.route('/reservations', methods=['GET'])
@login_required
def get_reservations():
    """Get user's reservations."""
    if current_user.is_admin:
        # Admin sees all reservations
        reservations = Reservation.query.all()
    else:
        # User sees only their reservations
        reservations = Reservation.query.filter_by(user_id=current_user.id).all()
    
    reservation_data = []
    for reservation in reservations:
        spot = ParkingSpot.query.get(reservation.spot_id)
        lot = ParkingLot.query.get(spot.parking_lot_id) if spot else None
        
        # Calculate cost if reservation is completed
        cost = None
        duration = None
        if reservation.leaving_timestamp and reservation.parking_timestamp:
            duration = (reservation.leaving_timestamp - reservation.parking_timestamp).total_seconds() / 3600
            cost = duration * lot.price if lot else 0
        
        reservation_data.append({
            'id': reservation.id,
            'spot_id': reservation.spot_id,
            'lot_name': lot.prime_location_name if lot else 'Unknown',
            'parking_timestamp': reservation.parking_timestamp.isoformat(),
            'leaving_timestamp': reservation.leaving_timestamp.isoformat() if reservation.leaving_timestamp else None,
            'duration': duration,
            'cost': cost,
            'status': 'Active' if not reservation.leaving_timestamp else 'Completed',
            'user_name': reservation.user.full_name if current_user.is_admin else None
        })
    
    return jsonify(reservation_data)

@api_bp.route('/reservations/<int:reservation_id>/release', methods=['POST'])
@login_required
def release_spot(reservation_id):
    """Release a parking spot."""
    reservation = Reservation.query.get_or_404(reservation_id)
    
    # Check if user owns this reservation or is admin
    if not current_user.is_admin and reservation.user_id != current_user.id:
        return jsonify({
            'success': False,
            'message': 'You can only release your own reservations'
        }), 403
    
    if reservation.leaving_timestamp:
        return jsonify({
            'success': False,
            'message': 'This reservation has already been completed'
        }), 400
    
    try:
        # Calculate cost
        duration = (datetime.utcnow() - reservation.parking_timestamp).total_seconds() / 3600
        spot = ParkingSpot.query.get(reservation.spot_id)
        lot = ParkingLot.query.get(spot.parking_lot_id)
        cost = duration * lot.price
        
        # Update reservation
        reservation.leaving_timestamp = datetime.utcnow()
        reservation.parking_cost = cost
        
        # Update spot status
        spot.status = 'A'
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Spot released successfully',
            'cost': cost,
            'duration': duration
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Failed to release spot: {str(e)}'
        }), 500

@api_bp.route('/export-history', methods=['POST'])
@login_required
def export_history():
    """Export parking history as CSV."""
    # This would typically be handled by Celery for async processing
    # For now, we'll return a success message
    return jsonify({
        'success': True,
        'message': 'Export request submitted. You will receive an email when ready.'
    })

@api_bp.route('/users', methods=['GET'])
@login_required
def get_users():
    """Get all users (admin only)."""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Admin access required'}), 403
    
    users = User.query.filter_by(role='user').all()
    return jsonify([{
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'full_name': user.full_name,
        'created_at': user.created_at.isoformat() if user.created_at else None
    } for user in users])
