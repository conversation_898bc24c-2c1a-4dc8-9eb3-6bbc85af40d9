from flask_wtf import <PERSON>laskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError
from .models.user import User


class LoginForm(FlaskForm):
    """Form for user login."""
    username = <PERSON><PERSON><PERSON>('Email Address', validators=[
        DataRequired(message='Email is required'),
        Email(message='Please enter a valid email address')
    ])
    password = PasswordField('Password', validators=[
        DataRequired(message='Password is required')
    ])
    submit = SubmitField('Sign In')


class RegisterForm(FlaskForm):
    """Form for user registration."""
    email = StringField('Email Address', validators=[
        DataRequired(message='Email is required'),
        Email(message='Please enter a valid email address')
    ])
    full_name = StringField('Full Name', validators=[
        DataRequired(message='Full name is required'),
        Length(min=2, max=100, message='Full name must be between 2 and 100 characters')
    ])
    password = Password<PERSON>ield('Password', validators=[
        DataRequired(message='Password is required'),
        Length(min=6, message='Password must be at least 6 characters long')
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        DataRequired(message='Please confirm your password'),
        EqualTo('password', message='Passwords must match')
    ])
    submit = SubmitField('Create Account')

    def validate_email(self, field):
        """Custom validator to check if email already exists."""
        if User.query.filter_by(email=field.data).first():
            raise ValidationError('Email address is already registered.')
